﻿﻿<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="admin_login.aspx.cs" Inherits="Web1.admin_login" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <h2>后台管理员登录</h2>

    管理员用户名:<asp:TextBox ID="txt_admin_id" runat="server"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txt_admin_id" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    管理员密码:<asp:TextBox ID="txt_admin_pwd" runat="server" TextMode="Password"></asp:TextBox>
    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txt_admin_pwd" ErrorMessage="*"></asp:RequiredFieldValidator>
    <br /><br />

    <asp:Button ID="Button1" runat="server" OnClick="Button1_Click" Text="登录后台" />
    <asp:Button ID="Button2" runat="server" OnClick="Button2_Click" Text="返回首页" CausesValidation="false" />
    <br /><br />

    <asp:Label ID="Label1" runat="server" Text="" ForeColor="Red"></asp:Label>

</asp:Content>
