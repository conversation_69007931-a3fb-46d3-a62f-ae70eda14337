﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="q.master.cs" Inherits="Web1.q" %>

<!DOCTYPE html>

<html>
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>

    <style type="text/css">
        .auto-style1 {
            width: 800px;
        }
        .auto-style2 {
            width: 100%;
        }
        .auto-style3 {
            width: 78px;
        }
        .auto-style5 {
            background-color: #00FFCC;
        }
        .auto-style6 {
            color: #FFFFFF;
            background-color: #99CC00;
        }
        .auto-style7 {
            background-color:aliceblue;

        }
        .auto-left {
            background-color:cornsilk;
            width: 200px;
            height: 400px;
        }
        .auto-right {

            width: 600px;
            height: 400px;
        }
        .auto-style8 {
            width: 100%;
            background-color: #CCFFFF;
        }
        .auto-style9 {
            width: 60px;
            height: 20px;
        }
        .auto-style11 {
            height: 20px;
        }
        .auto-style12 {
            width: 65px;
            height: 31px;
        }
    </style>

</head>
<body>
    <form id="form1" runat="server">
        <div>

            <table cellpadding="0" cellspacing="0" class="auto-style1">
                <tr>
                    <td colspan="2">
                        <table cellpadding="0" cellspacing="0" class="auto-style2">
                            <tr>
                                <td class="auto-style3">
                                    <img alt="" class="auto-style12" src="Images/logo.gif" /></td>
                                <td>
                                    <table cellpadding="0" cellspacing="0" class="auto-style2">
                                        <tr>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink1" runat="server" NavigateUrl="~/index.aspx">首页</asp:HyperLink>
                                            </td>

                                                              <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink7" runat="server" NavigateUrl="~/allproduct.aspx">所有商品</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink2" runat="server" NavigateUrl="~/userAdd.aspx">注册</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink3" runat="server" NavigateUrl="~/login.aspx">登录</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink4" runat="server" NavigateUrl="~/cart.aspx">购物车</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink5" runat="server" NavigateUrl="~/myinfo.aspx">人个信息</asp:HyperLink>
                                            </td>
                                            <td class="auto-style5">
                                                <asp:HyperLink ID="HyperLink6" runat="server" NavigateUrl="~/admin_login.aspx" Visible="true">登入到后台</asp:HyperLink>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="5">
                                                <asp:Label ID="Label1" runat="server" CssClass="auto-style6" Text="您还没有登录"></asp:Label>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">您的位置：</td>
                </tr>
                <tr>
                    <td class="auto-left" valign="top">商品分类<br />
                        <br />
                    </td>
                    <td class="auto-right" valign="top">
                        <table cellpadding="0" cellspacing="0" class="auto-style8">
                            <tr>
                                <td class="auto-style11">
                                    <img alt="" class="auto-style9" src="Images/google_logo.gif" /><asp:TextBox ID="TextBox1" runat="server"></asp:TextBox>
                                    <asp:ImageButton ID="ImageButton1" runat="server" ImageUrl="~/Images/searchbutton.gif" OnClick="ImageButton1_Click" />
                                </td>
                            </tr>

                        </table>
                        <br />
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </td>
                </tr>
                <tr>
                    <td class="auto-style7">&nbsp;</td>
                    <td class="auto-style7">Copyright 2025</td>
                </tr>
            </table>

        </div>
    </form>
</body>
</html>
